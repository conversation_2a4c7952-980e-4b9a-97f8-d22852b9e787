import type { Except } from 'type-fest'

import type { FixMeAny } from 'src/types'
import type { ExcludeStrict } from 'src/types/utils'

import cache, { cacheableMethods } from './cache'

export const apiErrorTypes = {
  INVALID_API_CALL: 'INVALID_API_CALL',
  INVALID_JSON: 'INVALID_JSON',
} as const

type ResponseBody = {
  error: string | number | Record<string, any> | null
  message?: string
  result: unknown
}

export const handleJsonResponse = [
  (res: Response) =>
    res.json().then(
      (anyBody) => {
        const body: ResponseBody = anyBody
        if (res.ok && !body.error && !body.message) {
          if (body.result) {
            return body.result
          }

          throw new Error('Something went wrong, please try again')
        }

        if (res.ok && !body.error && body.message) {
          // Handling error for invalid session
          const error = new Error(body.message)
          error.name = apiErrorTypes.INVALID_API_CALL
          throw error
        }

        if (body.error !== null && typeof body.error === 'object') {
          throw body.error
        }

        const error = new Error(body.error === 1 ? body.message : (body.error as any))
        error.name = apiErrorTypes.INVALID_API_CALL
        throw error
      },
      (err: Error | Record<string, any>) => {
        if (!(err instanceof Error)) {
          throw err
        }
        const error = new Error(err.message)
        error.name = apiErrorTypes.INVALID_JSON
        throw error
      },
    ),
  (err: FixMeAny) => {
    const error = new Error(
      err.message === 'Failed to fetch'
        ? 'Check your network connection.'
        : err.message,
    )
    error.name = err.name
    throw error
  },
]

const url = (() => {
  if (ENV.CYPRESS_CT_ENV) {
    return ENV.DEVELOPMENT_ENDPOINT // from .env.cypress-ct
  }
  if (ENV.NODE_ENV === 'development') {
    return 'https://scdf-tfms.cartrack.com/jsonrpc/index.php'
    // return ENV.DEVELOPMENT_ENDPOINT // Replace with alternate URL for international testing
  }
  if (window.location.hostname.indexOf('fleetusa') === 0) {
    return 'https://fleetweb-us.cartrack.com/jsonrpc/index.php'
  }
  return '/jsonrpc/index.php'
})()

function apiFetch(
  method: string,
  params: Record<string, FixMeAny>,
  {
    noX,
    noParse,
    signal,
  }: { noX: boolean; noParse?: boolean; signal?: AbortSignal | null },
) {
  const result = fetch(url, {
    signal,
    method: 'POST',
    // Options requests are wildly slower in Chrome, setting text/plain prevents the need for an options request
    // https://bugs.chromium.org/p/chromium/issues/detail?id=803580 Relevant chromium issue
    headers: {
      'Content-Type': ENV.CYPRESS_CT_ENV
        ? /* cy.intercept only plays well with application/json */
          'application/json'
        : 'text/plain',
    },
    ...(ENV.NODE_ENV === 'development' ? { credentials: 'include' } : {}),
    body: JSON.stringify({
      version: '2.0',
      method,
      id: 10,
      params: noX ? params : { x: 'x', ...params },
    }),
  })
  return noParse ? result : result.then(...handleJsonResponse)
}

type ApiCallerParameters = Parameters<typeof apiCaller>
type ApiCallerOptions = ExcludeStrict<ApiCallerParameters[2], undefined>

export default function apiCaller(
  method: string,
  params: Record<string, FixMeAny> | undefined = {},
  {
    noX = false,
    updateCache = false,
    noParse = false,
    signal,
  }: {
    noX?: boolean
    updateCache?: boolean
    noParse?: boolean
    signal?: AbortSignal | null
  } = {},
) {
  // Default
  if (method in cacheableMethods && !updateCache)
    return cache
      .get(method, params)
      .then(
        (data) => data || cache.set(method, params, apiFetch(method, params, { noX })),
      )

  const fetcher = apiFetch(method, params, { noX, noParse, signal })

  return updateCache ? cache.set(method, params, fetcher) : fetcher
}

export const apiCallerNoX = <ResponseType = unknown>(
  method: string,
  params: Record<string, FixMeAny> | undefined = {},
  options?: Except<ApiCallerOptions, 'noX'>,
): Promise<ResponseType> => apiCaller(method, params, { ...options, noX: true })
