/* eslint-disable sonarjs/no-clear-text-protocols */
import { isEmpty } from 'lodash'
import type { Except } from 'type-fest'

import { getTfmsCartrackServicesPath } from 'duxs/tfms'
import { getReduxStore_DANGER } from 'src/ReduxStoreSyncProvider'

import { authUtils } from './auth-utils'

/**
 * Generic response interface for REST APIs that follow the Success/value pattern
 */
type RestApiResponse<T> = {
  Success: boolean
  value: T
}

/**
 * Configuration options for REST API calls
 */
type RestApiCallerOptions = {
  signal?: AbortSignal | null
  headers?: Record<string, string>
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'
  body?: unknown
  queryParams?: Record<string, string | number | boolean>
  timeout?: number
  credentials?: RequestCredentials
  expectSuccessValuePattern?: boolean
}

/**
 * Builds query string from parameters object
 */
const buildQueryString = (
  params: Record<string, string | number | boolean>,
): string => {
  const searchParams = new URLSearchParams()

  for (const [key, value] of Object.entries(params)) {
    searchParams.append(key, String(value))
  }

  return searchParams.toString()
}

export function getTFMSAPIPath(
  settings = JSON.parse(sessionStorage.getItem('userSettings') || '{}'),
) {
  // NOTE: be careful
  const state = getReduxStore_DANGER().getState()
  const tfmsPath = getTfmsCartrackServicesPath(state)

  if (ENV.NODE_ENV === 'development') {
    return 'https://scdf-tfms.cartrack.com/tfms-services-api'
  }

  if (!isEmpty(tfmsPath)) {
    return tfmsPath
  }

  if (!isEmpty(settings.tfmsCartrackServicesAPI)) {
    return settings.tfmsCartrackServicesAPI
  }

  const tryLocal = JSON.parse(localStorage.getItem('userSettings') || '{}')

  if (!isEmpty(tryLocal.tfmsCartrackServicesAPI)) {
    return tryLocal.tfmsCartrackServicesAPI
  }

  // returning undefined just to make the code cleaner
  return '/tfms-services-api'
}

/**
 * Generic REST API caller supporting all HTTP methods
 *
 * @param endpoint - API endpoint path
 * @param options - Request configuration options
 * @returns Promise resolving to the API response data
 */
export const restApiCaller = async <TResponseData>(
  endpoint: string,
  options: RestApiCallerOptions = {},
): Promise<TResponseData> => {
  const {
    signal = null,
    headers = {},
    method = 'GET',
    body,
    queryParams = {},
    timeout = 30000,
    credentials = 'same-origin',
    expectSuccessValuePattern = true,
  } = options

  try {
    // Build the full URL
    const baseUrl = getTFMSAPIPath()
    const queryString =
      Object.keys(queryParams).length > 0 ? `?${buildQueryString(queryParams)}` : ''
    const fullUrl = `${baseUrl}${endpoint}${queryString}`

    // Create abort controller for timeout
    const timeoutController = new AbortController()
    const timeoutId = window.setTimeout(() => timeoutController.abort(), timeout)

    // Combine signals if both exist
    const combinedSignal =
      signal && timeoutController.signal
        ? AbortSignal.any([signal, timeoutController.signal])
        : signal || timeoutController.signal

    const accessToken = authUtils.getJwtAccessToken_USE_ONLY_ON_API_CALLERS()

    // Prepare fetch options
    const fetchOptions: RequestInit = {
      method,
      signal: combinedSignal,
      credentials,
      headers: {
        'Content-Type': 'application/json',
        ...(accessToken ? { Authorization: 'Bearer ' + accessToken } : {}),
        ...headers,
      },
    }

    // Add body for methods that support it
    if (body && !['GET', 'HEAD'].includes(method)) {
      fetchOptions.body = typeof body === 'string' ? body : JSON.stringify(body)
    }

    // Make the API call
    const response = await fetch(fullUrl, fetchOptions)

    // Clear timeout
    clearTimeout(timeoutId)

    // Handle non-success HTTP status codes
    if (!response.ok) {
      const contentType = response.headers.get('content-type')

      if (contentType?.includes('application/json')) {
        const errorData = await response.json()

        // Check if it's a SCIM error with detail field
        if (errorData.detail && typeof errorData.detail === 'string') {
          throw new Error(errorData.detail)
        }

        // Handle other JSON error formats
        if (errorData.message) {
          throw new Error(errorData.message)
        }
      } else {
        const errorText = await response.text().catch(() => 'Unknown error')
        throw new Error(`HTTP ${response.status}: ${response.statusText}. ${errorText}`)
      }
    }

    // Handle empty responses (for DELETE operations, etc.)
    const contentType = response.headers.get('content-type')
    if (!contentType?.includes('application/json')) {
      // For non-JSON responses, return the text or empty object
      const textResponse = await response.text()
      return (textResponse || {}) as TResponseData
    }

    // Parse JSON response
    const jsonResponse = await response.json()

    // Handle APIs that follow Success/value pattern
    if (expectSuccessValuePattern) {
      const typedResponse = jsonResponse as RestApiResponse<TResponseData>

      if (typedResponse.Success) {
        return typedResponse.value
      }

      // Handle API-level errors
      const errorMessage =
        typeof typedResponse.value === 'object' &&
        typedResponse.value !== null &&
        'message' in typedResponse.value
          ? (typedResponse.value as { message: string }).message
          : 'API request failed'

      throw new Error(errorMessage)
    }

    // Return raw JSON response for APIs that don't follow Success/value pattern
    return jsonResponse as TResponseData
  } catch (error) {
    // Handle specific error types
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new Error('Request was aborted or timed out')
      }

      if (error.message === 'Failed to fetch') {
        throw new Error('Network error: Check your connection')
      }
    }

    // Re-throw other errors
    throw error
  }
}

/**
 * Convenience functions for specific HTTP methods
 */
export const restGet = <TResponseData>(
  endpoint: string,
  options: Except<RestApiCallerOptions, 'method'> = {},
): Promise<TResponseData> =>
  restApiCaller<TResponseData>(endpoint, { ...options, method: 'GET' })

export const restPost = <TResponseData>(
  endpoint: string,
  body?: unknown,
  options: Except<RestApiCallerOptions, 'method' | 'body'> = {},
): Promise<TResponseData> =>
  restApiCaller<TResponseData>(endpoint, { ...options, method: 'POST', body })

export const restPut = <TResponseData>(
  endpoint: string,
  body?: unknown,
  options: Except<RestApiCallerOptions, 'method' | 'body'> = {},
): Promise<TResponseData> =>
  restApiCaller<TResponseData>(endpoint, { ...options, method: 'PUT', body })

export const restPatch = <TResponseData>(
  endpoint: string,
  body?: unknown,
  options: Except<RestApiCallerOptions, 'method' | 'body'> = {},
): Promise<TResponseData> =>
  restApiCaller<TResponseData>(endpoint, { ...options, method: 'PATCH', body })

export const restDelete = <TResponseData>(
  endpoint: string,
  options: Except<RestApiCallerOptions, 'method'> = {},
): Promise<TResponseData> =>
  restApiCaller<TResponseData>(endpoint, { ...options, method: 'DELETE' })
