import { DateTime } from 'luxon'
import type { Except } from 'type-fest'
import { z } from 'zod'

import {
  bookingAdditionalLocationIdIdSchema,
  bookingEquipmentIdSchema,
  clientUserIdSchema,
  driverIdSchema,
  vehicleIdSchema,
} from 'api/types'
import { messages } from 'src/shared/formik'
import { ctIntl } from 'src/util-components/ctIntl'
import { createZodObjPathGetter } from 'src/util-functions/zod-utils'

import { BookingStatus } from '../../utils/constants'
import {
  DRIVER_SELECTION,
  EQUIPMENT_TYPE,
  JOURNEY_TYPE,
  VEHICLE_COMMANDER_SELECTION,
} from './constant'
import type { DrawerMode } from './types'

export const issuanceRequestSearchParamsSchema = z.discriminatedUnion('type', [
  z.object({ type: z.literal('add') }),
  z.object({ type: z.literal('edit'), id: z.string() }),
  z.object({ type: z.literal('approve'), id: z.string() }),
  z.object({ type: z.literal('view'), id: z.string() }),
  z.object({ type: z.literal('duplicate'), id: z.string() }),
])

export type SearchParams = z.infer<typeof issuanceRequestSearchParamsSchema>

// need to make fields nullable so Zod always run to the superRefine function
// will add the validation for required fields in superRefine
export const generateBookingSchema = ({
  carpoolBookingInAdvance,
  carpoolBookingInAdvanceUnit,
  carpoolMaximumBookingTime,
  carpoolMaximumBookingTimeUnit,
  carpoolAllowBackDateBooking,
  bookingPurposeByIdMap,
  mode,
  statusId,
}: {
  carpoolBookingInAdvance: number
  carpoolBookingInAdvanceUnit: string
  carpoolMaximumBookingTime: number
  carpoolMaximumBookingTimeUnit: string
  carpoolAllowBackDateBooking: boolean
  bookingPurposeByIdMap: Map<number, { id: number; label: string }>
  mode: DrawerMode
  statusId?: BookingStatus
}) =>
  z
    .object({
      // STEP 1
      purposeOfRequest: z.number().nullable(),
      requestDescription: z.string().nullable(),
      requestor: z.string().nullable(),
      bookingForOtherParty: z.boolean().default(false),
      requestedForClientUserId: clientUserIdSchema.nullable(),

      // STEP 2
      pickupTime: z.date().nullable(),
      dropoffTime: z.date().nullable(),
      pickupLocation: z.number().nullable(),
      journeyType: z
        .enum([JOURNEY_TYPE.RETURN, JOURNEY_TYPE.SINGLE, JOURNEY_TYPE.MULTIPLE])
        .default(JOURNEY_TYPE.RETURN),
      journeys: z
        .array(
          z.discriminatedUnion('type', [
            z.object({
              type: z.literal('location'),
              id: z.number().nullable(), // nullable for default value, will validate it in super define
            }),
            z.object({
              type: z.literal('additionalLocation'),
              id: bookingAdditionalLocationIdIdSchema,
            }),
            z.object({
              type: z.literal('freeText'),
              value: z
                .string()
                .min(6, {
                  message: 'Location must be at least 6 characters',
                })
                .max(100, {
                  message: 'Location must not exceed 100 characters',
                }),
            }),
          ]),
        )
        .default([]),

      // STEP 3
      driverSelection: z
        .enum([
          DRIVER_SELECTION.ANY,
          DRIVER_SELECTION.SELF_DRIVE,
          DRIVER_SELECTION.SPECIFIC,
        ])
        .default(DRIVER_SELECTION.ANY),
      // approve mode, vehicleId, driverId and vehicleCommanderId are required
      driverId: mode === 'approve' ? driverIdSchema : driverIdSchema.nullable(),
      vehicleId: mode === 'approve' ? vehicleIdSchema : vehicleIdSchema.nullable(),
      vehicleTypeId: z.number().nullable(),
      numberOfPassengers: z.number().nullable(),

      vehicleCommanderSelection: z
        .enum([
          VEHICLE_COMMANDER_SELECTION.ANY,
          VEHICLE_COMMANDER_SELECTION.SELF_COMMAND,
          VEHICLE_COMMANDER_SELECTION.SPECIFIC,
        ])
        .default(VEHICLE_COMMANDER_SELECTION.ANY),
      vehicleCommanderId:
        mode === 'approve' ? clientUserIdSchema : clientUserIdSchema.nullable(),
      equipmentType: z
        .enum([
          EQUIPMENT_TYPE.NO_EQUIPMENT,
          EQUIPMENT_TYPE.CAR_BOOT,
          EQUIPMENT_TYPE.LARGER_SPACE,
        ])
        .default(EQUIPMENT_TYPE.NO_EQUIPMENT),
      equipmentIds: z.array(bookingEquipmentIdSchema).default([]),
      uploadedImages: z
        .array(
          z.object({
            guid: z.string(),
            extension: z.string(),
            fileName: z.string(),
            contentType: z.string(),
            previewUrl: z.string(),
          }),
        )
        .default([]),
      // For edit and approve, the existing attachments saved before
      equipmentAttachmentIds: z.array(z.number()).default([]),
      remarks: z.string().nullable(),
    })
    .superRefine((data, ctx) => {
      // Skip validation for view mode
      if (mode === 'view') return

      const { createPath } = createZodObjPathGetter(data)

      // Driver validation based on driverSelection
      if (data.driverSelection === DRIVER_SELECTION.SPECIFIC && !data.driverId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: createPath(['driverId']),
          message: messages.required,
        })
      }

      // Vehicle Commander validation based on vehicleCommanderSelection
      if (
        data.vehicleCommanderSelection === VEHICLE_COMMANDER_SELECTION.SPECIFIC &&
        !data.vehicleCommanderId
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: createPath(['vehicleCommanderId']),
          message: messages.required,
        })
      }

      if (!data.purposeOfRequest) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: createPath(['purposeOfRequest']),
          message: messages.required,
        })
      } else {
        const selectedPurpose = bookingPurposeByIdMap.get(data.purposeOfRequest)

        if (
          selectedPurpose?.label.toLowerCase() === 'others' &&
          (!data.requestDescription || data.requestDescription.trim().length === 0)
        ) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: createPath(['requestDescription']),
            message: messages.required,
          })
        }
      }

      if (data.bookingForOtherParty && !data.requestedForClientUserId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: createPath(['requestedForClientUserId']),
          message: messages.required,
        })
      }

      if (!data.pickupLocation) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: createPath(['pickupLocation']),
          message: messages.required,
        })
      }

      // Vehicle type validation (always required now)
      if (!data.vehicleTypeId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: createPath(['vehicleTypeId']),
          message: messages.required,
        })
      }

      // Number of passengers validation
      if (!data.numberOfPassengers || data.numberOfPassengers <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: createPath(['numberOfPassengers']),
          message: ctIntl.formatMessage({
            id: 'carpool.booking.numberOfPassengers.required',
            defaultMessage:
              'Number of passengers is required and must be greater than 0',
          }),
        })
      }

      // vehicle id is required only for approve mode
      if (mode === 'approve' && !data.vehicleId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: createPath(['vehicleId']),
          message: messages.required,
        })
      }

      // pickkup time should be behind current time for returned/terminated bookings
      if (data.pickupTime) {
        if (
          !carpoolAllowBackDateBooking &&
          data.pickupTime < new Date() &&
          (!statusId ||
            (statusId && statusId === BookingStatus.BOOKING_STATUS_REQUESTED))
        ) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: createPath(['pickupTime']),
            message: ctIntl.formatMessage({
              id: 'carpool.booking.date.error.pickup.after.current.time',
            }),
          })
        }

        const inAdvanceAppliedDate = DateTime.now()
          .plus({ [carpoolBookingInAdvanceUnit]: carpoolBookingInAdvance })
          .toJSDate()

        if (carpoolBookingInAdvance > 0 && data.pickupTime > inAdvanceAppliedDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: createPath(['pickupTime']),
            message: ctIntl.formatMessage(
              { id: 'carpool.booking.date.error.in.advance' },
              { values: { inAdvanceAppliedDate } },
            ),
          })
        }
      } else {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: createPath(['pickupTime']),
          message: messages.required,
        })
      }

      // dropoff time validation
      if (!data.dropoffTime) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: createPath(['dropoffTime']),
          message: messages.required,
        })
      }

      if (data.pickupTime && data.dropoffTime) {
        if (data.pickupTime >= data.dropoffTime) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: createPath(['dropoffTime']),
            message: ctIntl.formatMessage({
              id: 'carpool.booking.date.error.dropoff.after.pickup',
            }),
          })
        }

        const maximumBookingTimeApplied = DateTime.fromJSDate(data.pickupTime)
          .plus({
            [carpoolMaximumBookingTimeUnit]: carpoolMaximumBookingTime,
          })
          .toJSDate()

        if (
          carpoolMaximumBookingTime > 0 &&
          data.dropoffTime &&
          data.dropoffTime > maximumBookingTimeApplied
        ) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: createPath(['dropoffTime']),
            message: ctIntl.formatMessage(
              { id: 'carpool.booking.date.error.max.booking.time' },
              {
                values: { carpoolMaximumBookingTime, carpoolMaximumBookingTimeUnit },
              },
            ),
          })
        }
      }

      if (
        (data.journeyType === JOURNEY_TYPE.MULTIPLE ||
          data.journeyType === JOURNEY_TYPE.SINGLE) &&
        (!data.journeys || data.journeys.length === 0)
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: createPath(['journeys']),
          message: 'At least one destination is required',
        })
      }

      // Validate each journey for empty values
      if (data.journeys && data.journeys.length > 0) {
        for (const [index, journey] of data.journeys.entries()) {
          if (journey.type === 'location' && journey.id === null) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              path: createPath(['journeys', index]),
              message: messages.required,
            })
          }
          if (
            journey.type === 'additionalLocation' &&
            (!journey.id || journey.id === null)
          ) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              path: createPath(['journeys', index]),
              message: messages.required,
            })
          }
          if (
            journey.type === 'freeText' &&
            (!journey.value || journey.value.trim().length === 0)
          ) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              path: createPath(['journeys', index]),
              message: messages.required,
            })
          }
        }
      }

      // if not no-equipment, then equipmentIds is required
      if (data.equipmentType !== 'no-equipment' && data.equipmentIds.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: createPath(['equipmentIds']),
          message: messages.required,
        })
      }
    })

export type BookingFormSchema = z.infer<ReturnType<typeof generateBookingSchema>>

export type ValidSchema = Except<
  BookingFormSchema,
  | 'purposeOfRequest'
  | 'pickupTime'
  | 'dropoffTime'
  | 'pickupLocation'
  | 'vehicleTypeId'
  | 'numberOfPassengers'
  | 'requestor'
> & {
  purposeOfRequest: number
  pickupTime: Date
  dropoffTime: Date
  pickupLocation: number
  vehicleTypeId: number
  numberOfPassengers: number
  requestor: string
}
